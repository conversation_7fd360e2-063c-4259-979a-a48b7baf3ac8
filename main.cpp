void get_image_patch(const cv::Mat& img, double u, double v, int r, cv::Mat& img_sub) {
  int iu     = u;
  int iv     = v;
  double du  = u - iu;
  double dv  = v - iv;
  double a00 = 1 - du - dv + du * dv;
  double a01 = du - du * dv;
  double a10 = dv - du * dv;
  double a11 = du * dv;

  img_sub.create(2 * r + 1, 2 * r + 1, CV_64F);
  for(int j = -r; j <= r; ++j) {
    for(int i = -r; i <= r; ++i) {
      img_sub.at<double>(j + r, i + r) =
          a00 * img.at<double>(iv + j, iu + i) + a01 * img.at<double>(iv + j, iu + i + 1) +
          a10 * img.at<double>(iv + j + 1, iu + i) + a11 * img.at<double>(iv + j + 1, iu + i + 1);
    }
  }
}

void get_init_location(const cv::Mat& img, const cv::Mat& img_du, const cv::Mat& img_dv,
                       Corner& corners, const Params& params) {
  DetectMethod detect_method = params.corner_type == MonkeySaddlePoint ? HessianResponse : params.detect_method;
  switch(detect_method) {
  case TemplateMatchFast:
  case TemplateMatchSlow: {
    // templates and scales
    std::vector<double> tprops;
    if(detect_method == TemplateMatchFast) {
      tprops = {0, M_PI_2,
                M_PI_4, -M_PI_4};
    } else {
      tprops = {0, M_PI_2,
                M_PI_4, -M_PI_4,
                0, M_PI_4,
                0, -M_PI_4,
                M_PI_4, M_PI_2,
                -M_PI_4, M_PI_2,
                -3 * M_PI / 8, 3 * M_PI / 8,
                -M_PI / 8, M_PI / 8,
                -M_PI / 8, -3 * M_PI / 8,
                M_PI / 8, 3 * M_PI / 8};
    }

    // for all scales do
    for(const auto& r : params.radius) {
      // filter image
      cv::Mat img_corners = cv::Mat::zeros(img.size(), CV_64F);
      cv::Mat img_corners_a1, img_corners_a2, img_corners_b1, img_corners_b2, img_corners_mu,
          img_corners_a, img_corners_b, img_corners_s1, img_corners_s2;

      for(int i = 0; i < tprops.size(); i += 2) {
        std::vector<cv::Mat> template_kernel(4); // a1, a2, b1, b2
        create_correlation_patch(template_kernel, tprops[i], tprops[i + 1], r);

        // filter image with current template
        cv::filter2D(img, img_corners_a1, -1, template_kernel[0], cv::Point(-1, -1), 0, cv::BORDER_REPLICATE);
        cv::filter2D(img, img_corners_a2, -1, template_kernel[1], cv::Point(-1, -1), 0, cv::BORDER_REPLICATE);
        cv::filter2D(img, img_corners_b1, -1, template_kernel[2], cv::Point(-1, -1), 0, cv::BORDER_REPLICATE);
        cv::filter2D(img, img_corners_b2, -1, template_kernel[3], cv::Point(-1, -1), 0, cv::BORDER_REPLICATE);

        // compute mean
        img_corners_mu = (img_corners_a1 + img_corners_a2 + img_corners_b1 + img_corners_b2) / 4;

        // case 1: a=white, b=black
        img_corners_a  = cv::min(img_corners_a1, img_corners_a2) - img_corners_mu;
        img_corners_b  = img_corners_mu - cv::max(img_corners_b1, img_corners_b2);
        img_corners_s1 = cv::min(img_corners_a, img_corners_b);
        // case 2: b=white, a=black
        img_corners_a  = img_corners_mu - cv::max(img_corners_a1, img_corners_a2);
        img_corners_b  = cv::min(img_corners_b1, img_corners_b2) - img_corners_mu;
        img_corners_s2 = cv::min(img_corners_a, img_corners_b);

        // combine both
        img_corners = cv::max(img_corners, cv::max(img_corners_s1, img_corners_s2));
      }
      non_maximum_suppression(img_corners, 1, params.init_loc_thr, r, corners);
    }
    break;
  }
  case HessianResponse: {
    cv::Mat gauss_img;
    cv::GaussianBlur(img, gauss_img, cv::Size(7, 7), 1.5, 1.5);
    cv::Mat hessian_img;
    hessian_response(gauss_img, hessian_img);
    double mn = 0, mx = 0;
    cv::minMaxIdx(hessian_img, &mn, &mx, NULL, NULL);
    hessian_img = cv::abs(hessian_img);
    double thr  = std::abs(mn * params.init_loc_thr);
    for(const auto& r : params.radius) {
      non_maximum_suppression(hessian_img, r, thr, r, corners);
    }
    break;
  }
  case LocalizedRadonTransform: {
    cv::Mat response_img;
    localized_radon_transform(img, response_img);
    for(const auto& r : params.radius) {
      non_maximum_suppression(response_img, r, params.init_loc_thr / 10.0, r, corners);
    }
    break;
  }
  default:
    break;
  }

  // location refinement
  int width = img.cols, height = img.rows;
  cv::parallel_for_(cv::Range(0, corners.p.size()), [&](const cv::Range& range) -> void {
    for(int i = range.start; i < range.end; ++i) {
      double u = corners.p[i].x;
      double v = corners.p[i].y;
      int r    = corners.r[i];

      cv::Mat G = cv::Mat::zeros(2, 2, CV_64F);
      cv::Mat b = cv::Mat::zeros(2, 1, CV_64F);

      // get subpixel gradiant
      cv::Mat img_du_sub, img_dv_sub;
      if(u - r < 0 || u + r >= width - 1 || v - r < 0 || v + r >= height - 1) {
        break;
      }
      get_image_patch(img_du, u, v, r, img_du_sub);
      get_image_patch(img_dv, u, v, r, img_dv_sub);

      for(int j2 = 0; j2 < 2 * r + 1; ++j2) {
        for(int i2 = 0; i2 < 2 * r + 1; ++i2) {
          // pixel orientation vector
          double o_du   = img_du_sub.at<double>(j2, i2);
          double o_dv   = img_dv_sub.at<double>(j2, i2);
          double o_norm = std::sqrt(o_du * o_du + o_dv * o_dv);
          if(o_norm < 0.1) {
            continue;
          }

          // do not consider center pixel
          if(i2 == r && j2 == r) {
            continue;
          }
          G.at<double>(0, 0) += o_du * o_du;
          G.at<double>(0, 1) += o_du * o_dv;
          G.at<double>(1, 0) += o_du * o_dv;
          G.at<double>(1, 1) += o_dv * o_dv;
          b.at<double>(0, 0) += o_du * o_du * (i2 - r + u) + o_du * o_dv * (j2 - r + v);
          b.at<double>(1, 0) += o_du * o_dv * (i2 - r + u) + o_dv * o_dv * (j2 - r + v);
        }
      }

      cv::Mat new_pos = G.inv() * b;
      if(std::abs(new_pos.at<double>(0, 0) - corners.p[i].x) +
             std::abs(new_pos.at<double>(1, 0) - corners.p[i].y) <
         corners.r[i] * 2) {
        corners.p[i].x = new_pos.at<double>(0, 0);
        corners.p[i].y = new_pos.at<double>(1, 0);
      }
    }
  });
}

请详细解释下位置精细化部分的原理